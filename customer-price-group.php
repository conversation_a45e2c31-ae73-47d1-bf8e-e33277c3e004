<?php
/**
 * Plugin Name: Custom Price Group
 * Description: A plugin to assign customer id, price group, company & country code to the user.
 * Version: 1.0
 * Author: ATAK Interactive
 * License: GPL2
 * Text Domain: customer-price-group
 */


add_action('admin_menu', 'customer_price_group_script');
function customer_price_group_script()
{
    wp_register_script('customer-price-group-js', plugins_url('assets/js/customer-price-group.js', __FILE__), array('jquery'), null, true);
    wp_enqueue_script('customer-price-group-js');
    wp_localize_script('customer-price-group-js', 'ajax_obj', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('cpi_import_nonce'),
    ));
}

add_action('show_user_profile', 'custom_user_fields');
add_action('edit_user_profile', 'custom_user_fields');
// add_action('user_new_form', 'custom_user_fields');

$current_user_price_id = "";
function custom_user_fields($user)
{
    global $wpdb, $current_user_price_id;
    $main_user_id = get_main_b2b_admin_id($user->ID);
    $current_company_code = get_user_meta($main_user_id, '_companycode', true);
    $current_country_code = get_user_meta($main_user_id, '_country', true);
    $current_user_price_id = $current_price_group_id = get_user_meta($main_user_id, '_pricegroup', true);
    $customer_id = get_user_meta($main_user_id, '_customer', true);
    $sub_user = check_user_role_b2b_administrator($user->ID) ? "" : "readonly";

    ?>
    <h3>Customer price group settings</h3>
    <input type="text" id="is-main-user-role" value="<?php echo $sub_user ?>" hidden />
    <input type="text" id="current_price_group_id" value="<?php echo $current_price_group_id ?>" hidden />
    <table class="form-table">
        <tr>
            <th><label for="customer_id">Customer ID</label></th>
            <td>
                <select name="_customer" id="customer_id" <?php echo is_object($user) ? '' : 'required'; ?> <?php echo $sub_user ?>>
                    <option value="">Select a Customer ID</option>
                    <?php
                    // Fetch customer IDs from wp_sap_soldto_customers table
                    $customer_options = fetch_customer_ids_from_database();
                    foreach ($customer_options as $customer_option) {
                        $selected = selected($customer_id, $customer_option['customer_id'], false);
                        echo '<option value="' . esc_attr($customer_option['customer_id']) . '" ' . $selected . '>';
                        echo esc_html($customer_option['customer_id']);
                        // Show additional info if available
                        $info_parts = [];
                        if (!empty($customer_option['company_code'])) {
                            $info_parts[] = 'CC: ' . $customer_option['company_code'];
                        }
                        if (!empty($customer_option['country_code'])) {
                            $info_parts[] = 'Country: ' . $customer_option['country_code'];
                        }
                        if (!empty($info_parts)) {
                            echo ' (' . implode(', ', $info_parts) . ')';
                        }
                        echo '</option>';
                    }
                    ?>
                </select>
                <p class="description">Select Customer ID from the database.</p>
            </td>
        </tr>
        <tr>
            <th><label for="company_code">Company Code</label></th>
            <td>
                <input type="text" name="_companycode" id="company_code"
                    value="<?php echo esc_attr($current_company_code); ?>" placeholder="Auto-populated from database" readonly />
                <p class="description">Auto-populated when Customer ID is selected.</p>
            </td>
        </tr>
        <tr>
            <th><label for="country_code">Country Code</label></th>
            <td>
                <input type="text" name="_country" id="country_code" value="<?php echo esc_attr($current_country_code); ?>"
                    placeholder="Auto-populated from database" readonly />
                <p class="description">Auto-populated when Customer ID is selected.</p>
            </td>
        </tr>
        <tr>
            <th><label for="price_group">Price Group</label></th>
            <td>
                <input type="text" name="_pricegroup" id="price_group"
                    value="<?php echo esc_attr($current_price_group_id); ?>" placeholder="Auto-populated from database" readonly />
                <p class="description">Auto-populated when Customer ID is selected.</p>
            </td>
        </tr>
    </table>
    <?php
}


function render_price_group_dropdown($user)
{
    global $wpdb;

    // Fetch current meta values
    $current_country = is_object($user) && isset($user->ID) ? get_user_meta($user->ID, '_country', true) : '';
    $current_price_group_id = is_object($user) && isset($user->ID) ? get_user_meta($user->ID, '_pricegroup', true) : '';

    // Determine table dynamically based on the country code
    $table = ''; // Default table
    if ($current_country == 'US') {
        $table = 'wp_price_group';
    } else {
        if ($current_country == 'GB') {
            $table = 'wp_price_group_gbp';
        } else {
            $table = 'wp_price_group_eur';
        }
    }

    // Fetch all price groups from the appropriate table
    $price_groups = $wpdb->get_results("SELECT id, price_group_title FROM {$table}", OBJECT_K);

    ?>
    <select name="_pricegroup" id="price_group" required>
        <option value="">Select a price group</option>
        <?php foreach ($price_groups as $id => $group) { ?>
            <option value="<?php echo esc_attr($id); ?>" <?php selected($current_price_group_id, $id); ?>>
                <?php echo esc_html($group->price_group_title); ?>
            </option>
        <?php } ?>
    </select>
    <?php
}

function save_custom_user_fields($user_id)
{
    if (check_user_role_b2b_administrator($user_id)) {
		update_user_meta($user_id, '_parent_admin_id', "");
        if (isset($_POST['_customer'])) {
            update_user_meta($user_id, '_customer', sanitize_text_field($_POST['_customer']));
        }

        if (isset($_POST['_companycode'])) {
            update_user_meta($user_id, '_companycode', sanitize_text_field($_POST['_companycode']));
        }

        if (isset($_POST['_country'])) {
            update_user_meta($user_id, '_country', sanitize_text_field($_POST['_country']));
        }

        if (isset($_POST['_pricegroup'])) {
            update_user_meta($user_id, '_pricegroup', sanitize_text_field($_POST['_pricegroup']));
        }
    } else {
        global $wpdb, $current_user_price_id;
        $admin_user_id = "";
        $args = array(
            'meta_key' => '_customer',
            'meta_value' => sanitize_text_field($_POST['_customer']),
        );
        $user_query = new WP_User_Query($args);

        // Check if users were found
        if (!empty($user_query->get_results())) {
            foreach ($user_query->get_results() as $user) {
                $admin_user_id = $user->ID;
            }
            update_user_meta($user_id, '_parent_admin_id', $admin_user_id);
            update_user_meta($user_id, '_customer', '');
        }
    }

}
add_action('personal_options_update', 'save_custom_user_fields');
add_action('edit_user_profile_update', 'save_custom_user_fields');
add_action('user_register', 'save_custom_user_fields');


function fetch_price_groups($company_code, $country_code)
{
    global $wpdb;
    $table = 'wp_price_group'; // Default table
    if ($company_code == '4554') {
        if ($country_code == 'GB') {
            $table = 'wp_price_group_gbp';
        } else {
            $table = 'wp_price_group_eur';
        }
    }

    $price_groups = $wpdb->get_results("SELECT id, price_group_title FROM {$table}", OBJECT_K);

    $result = [];
    foreach ($price_groups as $id => $group) {
        $result[$id] = $group->price_group_title;
    }

    return $result;
}

function fetch_price_groups_handler()
{
    $company_code = $_POST['company_code'];
    $country_code = $_POST['country_code'];
    $res = fetch_price_groups($company_code, $country_code);
    if ($res) {
        return $res;
    } else {
        return [];
    }
    wp_die();
}
add_action('wp_ajax_fetch_price_groups', 'fetch_price_groups_handler');
// add_action('wp_ajax_nopriv_fetch_price_groups', 'fetch_price_groups');

function validate_customer_id_field($errors, $sanitized_user_login, $user_email)
{
    $screen = get_current_screen();
    if ($screen && $screen->id === 'user-edit') {
        // Only require Customer ID - other fields are auto-populated
        if (empty($_POST['_customer'])) {
            $errors->add('customer_id_error', '<strong>Error</strong>: Customer ID is required. Please select a Customer ID from the dropdown.');
        }

        if (isset($_POST['_customer']) && $_POST['role'] == "b2b_administrator") {
            $args = array(
                'meta_key' => '_customer',
                'meta_value' => sanitize_text_field($_POST['_customer']),
            );
            $user_query = new WP_User_Query($args);
            // var_dump(!empty($user_query->get_results()));
            // exit;
            if (!empty($user_query->get_results())) {
                $errors->add('customer_id_error', '<strong>Error</strong>: Customer ID already exists. You should use a new customer id for making this customer as a B2b Administrator.');
            }
        }
        return $errors;
    }
}
add_filter('user_profile_update_errors', 'validate_customer_id_field', 10, 3);

function fetch_price_group_list()
{
    // Validate the request
    if (!isset($_POST['company_code'], $_POST['country_code'])) {
        wp_send_json_error(['message' => 'Invalid data']);
    }

    $company_code = sanitize_text_field($_POST['company_code']);
    $country_code = sanitize_text_field($_POST['country_code']);
    $price_groups = [];

    global $wpdb, $current_user_price_id;

    // Logic for selecting the appropriate price group table
    if ($company_code == '3090') {
        $table_name = $wpdb->prefix . 'price_group';
    } elseif ($country_code === 'GB') {
        $table_name = $wpdb->prefix . 'price_group_gbp';
    } else {
        $table_name = $wpdb->prefix . 'price_group_eur';
    }

    // Fetch price groups from the table
    $results = $wpdb->get_results("SELECT id, price_group_title FROM {$table_name}", ARRAY_A);

    if ($results) {
        foreach ($results as $row) {
            $price_groups[$row['id']] = $row['price_group_title'];
        }
    }

    wp_send_json_success(['price_groups' => $price_groups, 'cur_id' => $current_user_price_id]);
}

add_action('wp_ajax_fetch_price_groups', 'fetch_price_group_list');
// add_action('wp_ajax_nopriv_fetch_price_groups', 'fetch_price_group_list');

function fetch_company_country_codes()
{
    // Validate the request
    if (!isset($_POST['customer_id'])) {
        wp_send_json_error(['message' => 'Invalid customer id.']);
    }

    $country_code = "";
    $company_code = "";
    $price_group = "";
    $customer_id = $_POST['customer_id'];
    $users_list = [];
    global $wpdb, $current_user_price_id;

    // First, try to get data from wp_sap_soldto_customers table
    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    // Debug: Log the query being executed
    error_log("Customer Price Group Debug: Looking for customer_id = " . $customer_id . " in table " . $table_name);

    $sap_customer = $wpdb->get_row($wpdb->prepare(
        "SELECT company_code, country_code, price_group FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ));

    // Debug: Log what was found
    if ($sap_customer) {
        error_log("Customer Price Group Debug: Found SAP customer data - CC: " . $sap_customer->company_code . ", Country: " . $sap_customer->country_code . ", PG: " . $sap_customer->price_group);
        $company_code = $sap_customer->company_code;
        $country_code = $sap_customer->country_code;
        $price_group = $sap_customer->price_group;
    } else {
        error_log("Customer Price Group Debug: No SAP customer data found for customer_id = " . $customer_id);
        // Check if there was a database error
        if ($wpdb->last_error) {
            error_log("Customer Price Group Debug: Database error - " . $wpdb->last_error);
        }
    }

    // Also check existing WordPress users with this customer ID
    $args = array(
        'meta_key' => '_customer',
        'meta_value' => $customer_id,
    );
    $user_query = new WP_User_Query($args);

    // If users exist, get their metadata (this will override SAP data if available)
    if (!empty($user_query->get_results())) {
        foreach ($user_query->get_results() as $user) {
            $users_list[] = $user->ID;
            $user_country = get_user_meta($user->ID, '_country', true);
            $user_company = get_user_meta($user->ID, '_companycode', true);
            $user_price_group = get_user_meta($user->ID, '_pricegroup', true);

            // Use user metadata if available
            if (!empty($user_country)) $country_code = $user_country;
            if (!empty($user_company)) $company_code = $user_company;
            if (!empty($user_price_group)) $price_group = $user_price_group;
        }
    }

    // Debug: Log final response data
    error_log("Customer Price Group Debug: Final response - CC: " . $company_code . ", Country: " . $country_code . ", PG: " . $price_group);

    wp_send_json_success([
        'company_code' => $company_code,
        'country_code' => $country_code,
        'price_group' => $price_group,
        'user_list' => $users_list,
        'source' => $sap_customer ? 'sap_database' : 'user_metadata'
    ]);

    wp_die();
}

add_action('wp_ajax_fetch_company_country_codes', 'fetch_company_country_codes');

function check_user_role_b2b_administrator($user_id) {
    $user = get_userdata($user_id);
    if ($user && !empty($user->roles)) {
        return in_array('b2b_administrator', $user->roles, true);
    }
    return false;
}

/**
 * Fetch customer IDs from wp_sap_soldto_customers table
 */
function fetch_customer_ids_from_database() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_soldto_customers';

    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
        return [];
    }

    // Fetch customer IDs, ordered by customer_id
    // Note: Your database has columns: id, customer_id, company_code, country_code, price_group
    $results = $wpdb->get_results(
        "SELECT DISTINCT customer_id, company_code, country_code, price_group
         FROM {$table_name}
         WHERE customer_id IS NOT NULL AND customer_id != ''
         ORDER BY customer_id ASC",
        ARRAY_A
    );

    if (!$results) {
        return [];
    }

    return $results;
}