jQuery(document).ready(function($) {
    // Handle customer ID dropdown change
    $('#customer_id').on('change', function() {
        var customerId = $(this).val();
        
        if (customerId) {
            // Show loading state
            $('#company_code, #country_code, #price_group').val('Loading...');
            
            // Make AJAX request to fetch customer data
            $.ajax({
                url: ajax_obj.ajax_url,
                type: 'POST',
                data: {
                    action: 'fetch_company_country_codes',
                    customer_id: customerId,
                    nonce: ajax_obj.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Populate the fields with data from database
                        $('#company_code').val(response.data.company_code || '');
                        $('#country_code').val(response.data.country_code || '');
                        $('#price_group').val(response.data.price_group || '');
                        
                        // Log source for debugging
                        if (response.data.source) {
                            console.log('Customer data loaded from: ' + response.data.source);
                        }
                        
                        // Show success message if data was found
                        if (response.data.company_code || response.data.country_code || response.data.price_group) {
                            showNotice('Customer information auto-populated successfully!', 'success');
                        } else {
                            showNotice('Customer ID found but no additional data available.', 'warning');
                        }
                    } else {
                        // Clear fields on error
                        $('#company_code, #country_code, #price_group').val('');
                        showNotice('Error loading customer data: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Clear fields on error
                    $('#company_code, #country_code, #price_group').val('');
                    showNotice('Network error loading customer data. Please try again.', 'error');
                    console.error('AJAX Error:', error);
                }
            });
        } else {
            // Clear fields when no customer is selected
            $('#company_code, #country_code, #price_group').val('');
        }
    });
    
    // Function to show admin notices
    function showNotice(message, type) {
        // Remove existing notices
        $('.customer-price-group-notice').remove();
        
        // Create notice element
        var noticeClass = 'notice notice-' + type + ' customer-price-group-notice';
        var notice = $('<div class="' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
        
        // Add dismiss button functionality
        notice.append('<button type="button" class="notice-dismiss"><span class="screen-reader-text">Dismiss this notice.</span></button>');
        
        // Insert notice after the page title
        if ($('.wrap h1').length) {
            $('.wrap h1').after(notice);
        } else {
            $('.wrap').prepend(notice);
        }
        
        // Handle dismiss button click
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(300, function() {
                $(this).remove();
            });
        });
        
        // Auto-hide success and warning notices after 5 seconds
        if (type === 'success' || type === 'warning') {
            setTimeout(function() {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }
    
    // Prevent manual editing of read-only fields
    $('#company_code, #country_code, #price_group').on('keydown paste', function(e) {
        e.preventDefault();
        showNotice('This field is auto-populated from the database. Please select a Customer ID to update it.', 'info');
        return false;
    });
    
    // Add visual styling for read-only fields
    $('#company_code, #country_code, #price_group').css({
        'background-color': '#f9f9f9',
        'cursor': 'not-allowed'
    });
});
