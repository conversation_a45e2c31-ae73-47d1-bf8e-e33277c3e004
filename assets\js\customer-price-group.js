jQuery(document).ready(function ($) {
    console.log("Customer-price")
    function updatePriceGroupList() {
        let companyCode = $('#company_code').val();
        let countryCode = $('#country_code').val();

        if (!companyCode || !countryCode) return;

        console.log(companyCode, '  ', countryCode)
        // AJAX request to fetch price groups
        $.ajax({
            url: ajaxurl, // WordPress AJAX URL
            method: 'POST',
            data: {
                action: 'fetch_price_groups',
                company_code: companyCode,
                country_code: countryCode,
            },
            success: function (response) {
                if (response.success) {
                    let priceGroupDropdown = $('#price_group');
                    priceGroupDropdown.empty();
                    let curId = response.data.cur_id;
                    console.log("CurId :" + curId)
                    // Populate the dropdown with new options
                    $.each(response.data.price_groups, function (id, title) {
                        let selectedFlag = "";
                        if (curId == title) {
                            selectedFlag = "selected";
                        }
                        priceGroupDropdown.append(
                            `<option value="${title}" ${selectedFlag}>${title}</option>`
                        );
                    });

                    // Retain previously selected value if possible
                    let currentPriceGroup = priceGroupDropdown.data('current-value');
                    if (currentPriceGroup) {
                        priceGroupDropdown.val(currentPriceGroup);
                    }
                }
            },
            error: function () {
                console.error('Failed to fetch price groups.');
            },
        });
    }

    function getPriceGroupListByCodes(companyCode, countryCode, curId){
        if (!companyCode || !countryCode) return;
        $.ajax({
            url: ajaxurl, // WordPress AJAX URL
            method: 'POST',
            data: {
                action: 'fetch_price_groups',
                company_code: companyCode,
                country_code: countryCode,
            },
            success: function (response) {
                if (response.success) {
                    let priceGroupDropdown = $('#price_group');
                    priceGroupDropdown.empty();
                    $.each(response.data.price_groups, function (id, title) {
                        priceGroupDropdown.append(
                            `<option value="${title}">${title}</option>`
                        );
                    });
                    $('#price_group').val(curId)
                    // let currentPriceGroup = priceGroupDropdown.data('current-value');
                    // if (currentPriceGroup) {
                    //     priceGroupDropdown.val(currentPriceGroup);
                    // }
                }
            },
            error: function () {
                console.error('Failed to fetch price groups.');
            },
        });
    }
    function getCompanyAndCountryCode(customerId){
        console.log('Making AJAX request for customer:', customerId);
        $.ajax({
            url: ajax_obj.ajax_url, // Use the correct AJAX URL from localized script
            method: 'POST',
            data: {
                action: 'fetch_company_country_codes',
                customer_id: customerId,
                nonce: ajax_obj.nonce
            },
            success: function (response) {
                console.log('AJAX Response received:', response);
                if (response.success) {
                    console.log('Response data:', response.data);
                    $('#company_code').val(response.data.company_code || '');
                    $('#country_code').val(response.data.country_code || '');
                    $('#price_group').val(response.data.price_group || '');

                    // Only call getPriceGroupListByCodes if we have the required data
                    if (response.data.company_code && response.data.country_code) {
                        getPriceGroupListByCodes(response.data.company_code, response.data.country_code, response.data.price_group);
                    }

                    console.log('Fields populated successfully');
                } else {
                    console.log('AJAX request failed:', response);
                    $('#company_code').val("");
                    $('#country_code').val("");
                    $('#price_group').empty();
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX Error fetching customer data:', error);
                console.error('XHR:', xhr);
                console.error('Status:', status);
            },
        });
    }
    // Handle customer ID dropdown change (changed from keyup to change for dropdown)
    $('body').on('change', '#customer_id', function(){
        var customerId = $(this).val();
        console.log('Customer ID selected from dropdown:', customerId);

        if(customerId && customerId.length > 0){
            getCompanyAndCountryCode(customerId);
        } else {
            // Clear fields when no customer is selected
            $('#company_code').val("");
            $('#country_code').val("");
            $('#price_group').empty();
        }
    })
    // Attach change events to trigger price group updates
    $('body').on('change', '#company_code, #country_code', function(){
        let companyCode = $('#company_code').val();
        let countryCode = $('#country_code').val();

        console.log(companyCode)
        console.log(countryCode)

    });
    // $('#company_code, #country_code').on('change', updatePriceGroupList);

    // Trigger update on page load (for edit user page)
    // updatePriceGroupList();



});
