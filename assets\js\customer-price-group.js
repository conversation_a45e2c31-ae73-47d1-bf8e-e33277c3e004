jQuery(document).ready(function ($) {
    console.log("🚀 Customer Price Group JS loaded");

    // Debug: Check if elements exist on page load
    console.log('🔍 Customer ID dropdown found:', $('#customer_id').length > 0 ? 'YES' : 'NO');
    console.log('🔍 Company Code field found:', $('#company_code').length > 0 ? 'YES' : 'NO');
    console.log('🔍 Country Code field found:', $('#country_code').length > 0 ? 'YES' : 'NO');
    console.log('🔍 Price Group field found:', $('#price_group').length > 0 ? 'YES' : 'NO');

    // Check if ajax_obj is available
    if (typeof ajax_obj !== 'undefined') {
        console.log('✅ AJAX object available:', ajax_obj.ajax_url);
    } else {
        console.error('❌ AJAX object not found! Check if script is properly localized.');
    }
    function updatePriceGroupList() {
        let companyCode = $('#company_code').val();
        let countryCode = $('#country_code').val();

        if (!companyCode || !countryCode) return;

        console.log(companyCode, '  ', countryCode)
        // AJAX request to fetch price groups
        $.ajax({
            url: ajaxurl, // WordPress AJAX URL
            method: 'POST',
            data: {
                action: 'fetch_price_groups',
                company_code: companyCode,
                country_code: countryCode,
            },
            success: function (response) {
                if (response.success) {
                    let priceGroupDropdown = $('#price_group');
                    priceGroupDropdown.empty();
                    let curId = response.data.cur_id;
                    console.log("CurId :" + curId)
                    // Populate the dropdown with new options
                    $.each(response.data.price_groups, function (id, title) {
                        let selectedFlag = "";
                        if (curId == title) {
                            selectedFlag = "selected";
                        }
                        priceGroupDropdown.append(
                            `<option value="${title}" ${selectedFlag}>${title}</option>`
                        );
                    });

                    // Retain previously selected value if possible
                    let currentPriceGroup = priceGroupDropdown.data('current-value');
                    if (currentPriceGroup) {
                        priceGroupDropdown.val(currentPriceGroup);
                    }
                }
            },
            error: function () {
                console.error('Failed to fetch price groups.');
            },
        });
    }

    function getPriceGroupListByCodes(companyCode, countryCode, curId){
        if (!companyCode || !countryCode) return;
        $.ajax({
            url: ajaxurl, // WordPress AJAX URL
            method: 'POST',
            data: {
                action: 'fetch_price_groups',
                company_code: companyCode,
                country_code: countryCode,
            },
            success: function (response) {
                if (response.success) {
                    let priceGroupDropdown = $('#price_group');
                    priceGroupDropdown.empty();
                    $.each(response.data.price_groups, function (id, title) {
                        priceGroupDropdown.append(
                            `<option value="${title}">${title}</option>`
                        );
                    });
                    $('#price_group').val(curId)
                    // let currentPriceGroup = priceGroupDropdown.data('current-value');
                    // if (currentPriceGroup) {
                    //     priceGroupDropdown.val(currentPriceGroup);
                    // }
                }
            },
            error: function () {
                console.error('Failed to fetch price groups.');
            },
        });
    }
    function getCompanyAndCountryCode(customerId){
        console.log('Making AJAX request for customer:', customerId);
        $.ajax({
            url: ajax_obj.ajax_url, // Use the correct AJAX URL from localized script
            method: 'POST',
            data: {
                action: 'fetch_company_country_codes',
                customer_id: customerId,
                nonce: ajax_obj.nonce
            },
            success: function (response) {
                console.log('AJAX Response received:', response);
                if (response.success) {
                    console.log('Response data:', response.data);

                    // Populate the read-only fields with data from database
                    $('#company_code').val(response.data.company_code || '');
                    $('#country_code').val(response.data.country_code || '');
                    $('#price_group').val(response.data.price_group || '');

                    // Show success message
                    if (response.data.company_code || response.data.country_code || response.data.price_group) {
                        console.log('Customer data loaded successfully from:', response.data.source || 'database');
                        console.log('Company Code:', response.data.company_code);
                        console.log('Country Code:', response.data.country_code);
                        console.log('Price Group:', response.data.price_group);
                    } else {
                        console.log('Customer ID found but no additional data available');
                    }
                } else {
                    console.log('AJAX request failed:', response);
                    $('#company_code').val("");
                    $('#country_code').val("");
                    $('#price_group').val("");
                }
            },
            error: function (xhr, status, error) {
                console.error('AJAX Error fetching customer data:', error);
                console.error('XHR:', xhr);
                console.error('Status:', status);
            },
        });
    }
    // Handle customer ID dropdown change (changed from keyup to change for dropdown)
    $('body').on('change', '#customer_id', function(){
        var customerId = $(this).val();
        console.log('🔄 Customer ID dropdown changed! Selected:', customerId);
        console.log('🔄 Dropdown element found:', $(this).length > 0 ? 'YES' : 'NO');

        if(customerId && customerId.length > 0){
            console.log('🚀 Calling getCompanyAndCountryCode for:', customerId);
            getCompanyAndCountryCode(customerId);
        } else {
            console.log('🧹 Clearing fields - no customer selected');
            // Clear fields when no customer is selected
            $('#company_code').val("");
            $('#country_code').val("");
            $('#price_group').val("");
        }
    })

    // Also add a direct event listener as backup
    $(document).on('change', '#customer_id', function(){
        console.log('🔄 BACKUP: Customer ID changed via document listener:', $(this).val());
    })
    // Attach change events to trigger price group updates
    $('body').on('change', '#company_code, #country_code', function(){
        let companyCode = $('#company_code').val();
        let countryCode = $('#country_code').val();

        console.log(companyCode)
        console.log(countryCode)

    });
    // $('#company_code, #country_code').on('change', updatePriceGroupList);

    // Trigger update on page load (for edit user page)
    // updatePriceGroupList();



});
